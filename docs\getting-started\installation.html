<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation Guide - Forge EC Documentation</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Complete installation guide for Forge EC. Learn multiple installation methods, dependency management, and feature configuration.">
    <meta name="keywords" content="forge ec, rust, installation, cargo, dependencies, setup">
    <meta name="author" content="Tanmay Patil">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Installation Guide - Forge EC Documentation">
    <meta property="og:description" content="Complete installation guide for Forge EC cryptography library">
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://forge-ec.dev/docs/getting-started/installation.html">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="../../css/animations.css">
    <link rel="stylesheet" href="../../css/components.css">
    <link rel="stylesheet" href="../docs.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="../../assets/favicon.svg">
    
    <!-- Syntax Highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8,20 Q20,8 32,20 Q20,32 8,20" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="20" cy="20" r="2" fill="currentColor"/>
                </svg>
            </div>
            <div class="loading-text">Loading Documentation...</div>
            <div class="loading-progress">
                <div class="loading-bar" id="loading-bar"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar docs-navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../../index.html" class="brand-link">
                    <svg class="brand-logo" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8,20 Q20,8 32,20 Q20,32 8,20" stroke="currentColor" stroke-width="2" fill="none"/>
                        <circle cx="20" cy="20" r="2" fill="currentColor"/>
                    </svg>
                    <span class="brand-text">Forge EC</span>
                </a>
            </div>

            <div class="nav-menu" id="nav-menu">
                <a href="../../index.html" class="nav-link">Home</a>
                <a href="../../index.html#features" class="nav-link">Features</a>
                <a href="../../index.html#about" class="nav-link">About</a>
                <a href="../../index.html#docs" class="nav-link active">Documentation</a>
                <a href="../../index.html#examples" class="nav-link">Examples</a>
                <a href="../../index.html#community" class="nav-link">Community</a>
                <a href="../../index.html#contact" class="nav-link">Contact</a>
            </div>

            <div class="nav-actions">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                    <svg class="theme-icon sun-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="5"/>
                        <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
                    </svg>
                    <svg class="theme-icon moon-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                    </svg>
                </button>
                <button class="auth-btn" id="auth-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <span>Sign In</span>
                </button>
                <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Toggle mobile menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Documentation Content -->
    <main class="docs-main">
        <!-- Breadcrumb Navigation -->
        <div class="breadcrumb-container">
            <div class="container">
                <nav class="breadcrumb" aria-label="Breadcrumb">
                    <ol class="breadcrumb-list">
                        <li class="breadcrumb-item">
                            <a href="../../index.html#docs" class="breadcrumb-link">Documentation</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="../getting-started.html" class="breadcrumb-link">Getting Started</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Installation Guide</li>
                    </ol>
                </nav>
            </div>
        </div>

        <!-- Documentation Header -->
        <div class="docs-header">
            <div class="container">
                <div class="docs-header-content">
                    <div class="docs-meta">
                        <span class="docs-category">Getting Started</span>
                        <span class="docs-level beginner">Beginner</span>
                        <span class="docs-time">3 min read</span>
                    </div>
                    <h1 class="docs-title">Installation Guide</h1>
                    <p class="docs-subtitle">
                        Learn how to install Forge EC using multiple methods, manage dependencies, 
                        and configure features for your specific use case.
                    </p>
                    <div class="docs-actions">
                        <button class="bookmark-btn" id="bookmark-btn" aria-label="Bookmark this page">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
                            </svg>
                        </button>
                        <div class="reading-progress">
                            <div class="progress-bar" id="reading-progress"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Documentation Content -->
        <div class="docs-content">
            <div class="container">
                <div class="docs-layout">
                    <!-- Table of Contents -->
                    <aside class="docs-sidebar">
                        <div class="toc-container glass-enhanced">
                            <h3 class="toc-title">Table of Contents</h3>
                            <nav class="toc" id="toc">
                                <ul class="toc-list">
                                    <li class="toc-item">
                                        <a href="#requirements" class="toc-link">System Requirements</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#cargo-install" class="toc-link">Cargo Installation</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#git-install" class="toc-link">Git Installation</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#features" class="toc-link">Feature Flags</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#verification" class="toc-link">Verify Installation</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#troubleshooting" class="toc-link">Troubleshooting</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </aside>

                    <!-- Main Content -->
                    <article class="docs-article">
                        <section id="requirements" class="docs-section">
                            <h2>System Requirements</h2>
                            <p>
                                Before installing Forge EC, ensure your system meets the following requirements:
                            </p>
                            
                            <div class="info-box">
                                <div class="info-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4M12 8h.01"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <strong>Minimum Requirements:</strong>
                                    <ul>
                                        <li><strong>Rust:</strong> 1.70.0 or later</li>
                                        <li><strong>Cargo:</strong> 1.70.0 or later</li>
                                        <li><strong>Memory:</strong> 512 MB RAM (2 GB recommended)</li>
                                        <li><strong>Storage:</strong> 50 MB free space</li>
                                    </ul>
                                </div>
                            </div>

                            <h3>Supported Platforms</h3>
                            <ul class="docs-list">
                                <li><strong>Linux:</strong> x86_64, aarch64 (Ubuntu 18.04+, RHEL 8+)</li>
                                <li><strong>macOS:</strong> x86_64, Apple Silicon (macOS 10.15+)</li>
                                <li><strong>Windows:</strong> x86_64 (Windows 10+)</li>
                                <li><strong>WebAssembly:</strong> wasm32-unknown-unknown</li>
                            </ul>
                        </section>

                        <section id="cargo-install" class="docs-section">
                            <h2>Cargo Installation</h2>
                            <p>
                                The recommended way to install Forge EC is through Cargo, Rust's package manager.
                            </p>

                            <h3>Method 1: Add to Cargo.toml</h3>
                            <p>Add Forge EC to your project's dependencies:</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Cargo.toml</span>
                                    <button class="copy-btn" data-copy='[dependencies]
forge-ec = "0.1.0"'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-toml">[dependencies]
forge-ec = "0.1.0"</code></pre>
                            </div>

                            <h3>Method 2: Command Line Installation</h3>
                            <p>Use Cargo's add command for automatic dependency management:</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Terminal</span>
                                    <button class="copy-btn" data-copy="cargo add forge-ec">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-bash">cargo add forge-ec</code></pre>
                            </div>

                            <h3>Method 3: Specific Version</h3>
                            <p>Install a specific version for compatibility:</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Terminal</span>
                                    <button class="copy-btn" data-copy="cargo add forge-ec@0.1.0">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-bash">cargo add forge-ec@0.1.0</code></pre>
                            </div>
                        </section>

                        <section id="git-install" class="docs-section">
                            <h2>Git Installation</h2>
                            <p>
                                Install directly from the Git repository for the latest development version:
                            </p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Cargo.toml</span>
                                    <button class="copy-btn" data-copy='[dependencies]
forge-ec = { git = "https://github.com/tanm-sys/forge-ec.git" }'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-toml">[dependencies]
forge-ec = { git = "https://github.com/tanm-sys/forge-ec.git" }</code></pre>
                            </div>

                            <h3>Specific Branch or Tag</h3>
                            <p>Install from a specific branch or tag:</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Cargo.toml</span>
                                    <button class="copy-btn" data-copy='[dependencies]
# Install from specific branch
forge-ec = { git = "https://github.com/tanm-sys/forge-ec.git", branch = "develop" }

# Install from specific tag
forge-ec = { git = "https://github.com/tanm-sys/forge-ec.git", tag = "v0.1.0" }'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-toml">[dependencies]
# Install from specific branch
forge-ec = { git = "https://github.com/tanm-sys/forge-ec.git", branch = "develop" }

# Install from specific tag
forge-ec = { git = "https://github.com/tanm-sys/forge-ec.git", tag = "v0.1.0" }</code></pre>
                            </div>

                            <div class="warning-box">
                                <div class="warning-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                        <line x1="12" y1="9" x2="12" y2="13"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17"/>
                                    </svg>
                                </div>
                                <div class="warning-content">
                                    <strong>Development Version Warning:</strong> Git installations may include unstable features 
                                    and breaking changes. Use stable releases for production applications.
                                </div>
                            </div>
                        </section>

                        <section id="features" class="docs-section">
                            <h2>Feature Flags</h2>
                            <p>
                                Forge EC supports optional features that can be enabled through Cargo feature flags:
                            </p>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Feature Configuration</span>
                                    <button class="copy-btn" data-copy='[dependencies]
forge-ec = { version = "0.1.0", features = ["serde", "wasm", "std"] }

# Available features:
# - "serde": Serialization support with serde
# - "wasm": WebAssembly compatibility
# - "std": Standard library support (enabled by default)
# - "alloc": Allocation support for no_std environments
# - "zeroize": Secure memory clearing'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-toml">[dependencies]
forge-ec = { version = "0.1.0", features = ["serde", "wasm", "std"] }

# Available features:
# - "serde": Serialization support with serde
# - "wasm": WebAssembly compatibility
# - "std": Standard library support (enabled by default)
# - "alloc": Allocation support for no_std environments
# - "zeroize": Secure memory clearing</code></pre>
                            </div>

                            <h3>Feature Descriptions</h3>
                            <ul class="docs-list">
                                <li><strong>serde:</strong> Enables serialization/deserialization with serde</li>
                                <li><strong>wasm:</strong> Optimizations for WebAssembly targets</li>
                                <li><strong>std:</strong> Standard library support (default)</li>
                                <li><strong>alloc:</strong> Heap allocation for no_std environments</li>
                                <li><strong>zeroize:</strong> Secure memory clearing for sensitive data</li>
                            </ul>
                        </section>

                        <section id="verification" class="docs-section">
                            <h2>Verify Installation</h2>
                            <p>
                                After installation, verify that Forge EC is working correctly:
                            </p>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Verification Test</span>
                                    <button class="copy-btn" data-copy='use forge_ec::signatures::ecdsa::*;

fn main() {
    println!("Testing Forge EC installation...");

    // This should compile and run without errors
    let private_key = PrivateKey::new();
    let public_key = private_key.public_key();

    println!("✅ Forge EC installed successfully!");
    println!("Public key: {}", public_key.to_hex());
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::signatures::ecdsa::*;

fn main() {
    println!("Testing Forge EC installation...");

    // This should compile and run without errors
    let private_key = PrivateKey::new();
    let public_key = private_key.public_key();

    println!("✅ Forge EC installed successfully!");
    println!("Public key: {}", public_key.to_hex());
}</code></pre>
                            </div>

                            <p>Run the verification test:</p>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Terminal</span>
                                    <button class="copy-btn" data-copy="cargo run">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-bash">cargo run</code></pre>
                            </div>
                        </section>

                        <section id="troubleshooting" class="docs-section">
                            <h2>Troubleshooting</h2>
                            <p>
                                Common installation issues and their solutions:
                            </p>

                            <h3>Compilation Errors</h3>
                            <div class="error-box">
                                <div class="error-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <line x1="15" y1="9" x2="9" y2="15"/>
                                        <line x1="9" y1="9" x2="15" y2="15"/>
                                    </svg>
                                </div>
                                <div class="error-content">
                                    <strong>Error:</strong> "package `forge-ec` cannot be found"
                                    <br><strong>Solution:</strong> Ensure you've added the dependency correctly and run `cargo update`
                                </div>
                            </div>

                            <h3>Version Conflicts</h3>
                            <div class="warning-box">
                                <div class="warning-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                        <line x1="12" y1="9" x2="12" y2="13"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17"/>
                                    </svg>
                                </div>
                                <div class="warning-content">
                                    <strong>Issue:</strong> Dependency version conflicts
                                    <br><strong>Solution:</strong> Use `cargo tree` to identify conflicts and update dependencies
                                </div>
                            </div>

                            <h3>Platform-Specific Issues</h3>
                            <ul class="docs-list">
                                <li><strong>Windows:</strong> Ensure you have the latest Visual Studio Build Tools</li>
                                <li><strong>macOS:</strong> Install Xcode command line tools: `xcode-select --install`</li>
                                <li><strong>Linux:</strong> Install build essentials: `sudo apt install build-essential`</li>
                            </ul>
                        </section>
                    </article>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="docs-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <p>&copy; 2024 Forge EC. Built with ❤️ by <a href="https://github.com/tanm-sys" target="_blank">Tanmay Patil</a></p>
                </div>
                <div class="footer-section">
                    <div class="footer-links">
                        <a href="../../index.html#about">About</a>
                        <a href="../../index.html#community">Community</a>
                        <a href="../security/vulnerability-disclosure.html">Security</a>
                        <a href="https://github.com/tanm-sys/forge-ec" target="_blank">GitHub</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase Configuration -->
    <script type="module">
        // Firebase CDN imports
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js';
        import { getFirestore } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js';
        import { getAuth } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDBG9YcnodA8Lhpwb3wOoyp93VcqXygcrQ",
            authDomain: "forge-ec.firebaseapp.com",
            databaseURL: "https://forge-ec-default-rtdb.firebaseio.com",
            projectId: "forge-ec",
            storageBucket: "forge-ec.firebasestorage.app",
            messagingSenderId: "436060720516",
            appId: "1:436060720516:web:4c4ac16371db82fcfd61d1",
            measurementId: "G-1BVB7FLGRJ"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);
        const auth = getAuth(app);

        // Make Firebase services globally available
        window.firebaseApp = app;
        window.firebaseDb = db;
        window.firebaseAuth = auth;
        window.firebaseInitialized = true;

        // Dispatch ready event
        window.dispatchEvent(new CustomEvent('firebaseReady'));
    </script>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="../docs.js"></script>

    <!-- Fallback loading screen timeout -->
    <script>
        // Ensure loading screen is hidden after maximum 10 seconds
        setTimeout(function() {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen && loadingScreen.style.display !== 'none') {
                console.warn('Loading screen timeout - hiding loading screen as fallback');
                loadingScreen.style.opacity = '0';
                setTimeout(function() {
                    loadingScreen.style.display = 'none';
                }, 300);
            }
        }, 10000);

        // Also hide loading screen when page is fully loaded as additional fallback
        window.addEventListener('load', function() {
            setTimeout(function() {
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen && loadingScreen.style.display !== 'none') {
                    console.log('Page loaded - hiding loading screen as fallback');
                    loadingScreen.style.opacity = '0';
                    setTimeout(function() {
                        loadingScreen.style.display = 'none';
                    }, 300);
                }
            }, 2000); // Wait 2 seconds after page load
        });
    </script>
</body>
</html>
